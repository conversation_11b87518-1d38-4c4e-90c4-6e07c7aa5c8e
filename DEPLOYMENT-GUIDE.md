# RVM to GLB Converter - Deployment Guide

This project is now structured with separate client and server directories for optimal deployment.

## Project Structure

```
project/
├── client/              # Frontend (HTML, CSS, JS)
│   ├── index.html
│   ├── script.js
│   ├── config.js
│   └── netlify.toml
├── server/              # Backend (Node.js Express)
│   ├── server.js
│   ├── package.json
│   ├── build.js
│   ├── download-rvmparser-linux.js
│   ├── rvmparser.exe    # Windows binary
│   ├── render.yaml
│   └── uploads/
└── README.md
```

## Deployment Steps

### 1. Deploy Server to Render

1. **Create a new Web Service on Render:**
   - Go to [render.com](https://render.com)
   - Click "New" → "Web Service"
   - Connect your GitHub repository
   - **Important:** Set the Root Directory to `server`

2. **Configure the service:**
   - **Name:** `grse-marine-server`
   - **Environment:** `Node`
   - **Region:** Choose closest to your users
   - **Branch:** `main` (or your default branch)
   - **Root Directory:** `server` ⚠️ **CRITICAL**
   - **Build Command:** `npm install && npm run build`
   - **Start Command:** `npm start`

3. **Environment Variables:**
   - `NODE_ENV`: `production`
   - `PORT`: (automatically set by Render)

4. **Deploy and get the URL:**
   - After deployment, you'll get a URL like: `https://grse-marine-server.onrender.com`
   - **Copy this URL** - you'll need it for the client configuration

### 2. Update Client Configuration

1. **Update the API URL in `client/config.js`:**
   ```javascript
   // Replace this line:
   return 'https://grse-marine.onrender.com';
   
   // With your actual Render server URL:
   return 'https://YOUR-ACTUAL-SERVER-URL.onrender.com';
   ```

### 3. Deploy Client to Netlify

1. **Create a new site on Netlify:**
   - Go to [netlify.com](https://netlify.com)
   - Click "Add new site" → "Import an existing project"
   - Connect your GitHub repository
   - **Important:** Set the Base directory to `client`

2. **Configure the site:**
   - **Base directory:** `client` ⚠️ **CRITICAL**
   - **Build command:** (leave empty)
   - **Publish directory:** `.` (current directory)

3. **Deploy:**
   - Click "Deploy site"
   - You'll get a URL like: `https://amazing-name-123456.netlify.app`

## Testing the Deployment

1. **Test the server directly:**
   ```bash
   curl https://YOUR-SERVER-URL.onrender.com/health
   ```
   Should return: `{"status":"OK","timestamp":"..."}`

2. **Test the client:**
   - Open your Netlify URL in a browser
   - Try uploading and converting an RVM file
   - Check browser console for any errors

## Troubleshooting

### Server Issues:
- **Binary not found:** The build script automatically downloads the Linux rvmparser binary
- **Permission denied:** The build script sets proper permissions for the binary
- **Port issues:** Render automatically sets the PORT environment variable

### Client Issues:
- **CORS errors:** Make sure the server URL in config.js is correct
- **API not found:** Verify the server is deployed and running

### Common Problems:
1. **Wrong root directory:** Make sure Render uses `server` and Netlify uses `client`
2. **Incorrect API URL:** Update `client/config.js` with your actual server URL
3. **Build failures:** Check the build logs on Render for specific errors

## Local Development

For local development, you can still run both from the root directory:

```bash
# Terminal 1 - Start server
cd server
npm install
npm start

# Terminal 2 - Serve client (optional, can open index.html directly)
cd client
# Open index.html in browser or use a local server
```

The client will automatically detect localhost and use `http://localhost:3000` for the API.
