# Deployment Guide

## Current Setup

### Frontend (Netlify)
- **Site URL**: Your Netlify frontend URL
- **Files**: `index.html`, `script.js`, `config.js`, `style.css` (if separate)

### Backend Server (Netlify Functions or separate hosting)
- **Server URL**: `https://devserver-main--grse-marine.netlify.app`
- **Files**: `server.js`, `package.json`, `rvmparser.exe`

## Configuration

The app automatically detects the environment:

- **Local Development**: Uses `http://localhost:3000`
- **Production**: Uses `https://devserver-main--grse-marine.netlify.app`

### To Update Server URL

Edit `config.js` and change the production URL:

```javascript
// Production server URL
return 'https://your-new-server-url.com';
```

## Deployment Steps

### 1. Frontend Deployment (Netlify)
1. Push your code to GitHub
2. Connect your GitHub repo to Netlify
3. Deploy from the main branch
4. Files to include: `index.html`, `script.js`, `config.js`

### 2. Backend Deployment Options

#### Option A: Netlify Functions (Recommended)
- Convert `server.js` to Netlify Functions
- Include `rvmparser.exe` in the deployment

#### Option B: Separate Server (Current)
- Deploy to a service that supports Node.js and binary files
- Ensure `rvmparser.exe` is executable
- Update CORS settings if needed

## Environment Variables

If you want to use environment variables instead of the config file:

1. Set `REACT_APP_API_URL` in your Netlify environment variables
2. Update `config.js` to check for this variable first

## Testing

1. **Local**: Visit `http://localhost:3000`
2. **Production**: Visit your Netlify URL
3. Test file upload and conversion

## Troubleshooting

- **CORS Issues**: Ensure your server has proper CORS headers
- **File Upload Fails**: Check server URL in browser console
- **Conversion Fails**: Verify `rvmparser.exe` is included and executable
