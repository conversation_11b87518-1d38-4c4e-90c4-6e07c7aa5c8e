# RVM to GLB Converter

A web application that converts AVEVA Marine RVM files to GLB format for 3D viewing in browsers and applications like Windows 3D Viewer.

## Features

- **Drag & Drop Interface**: Easy file upload with drag and drop support
- **Real-time Progress**: Visual progress indicator during conversion
- **Multiple Conversion Methods**: Supports various conversion backends
- **File Validation**: Validates file type and size before processing
- **Secure**: Files are automatically cleaned up after conversion
- **Responsive Design**: Works on desktop and mobile devices

## Supported Formats

- **Input**: RVM files from AVEVA Marine/PDMS
- **Output**: GLB (Binary glTF) files

## Quick Start

### Prerequisites

- Node.js (version 14 or higher)
- npm (Node Package Manager)

### Installation

1. **Clone or download the project files**
2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Start the server**:
   ```bash
   npm start
   ```

4. **Open your browser** and navigate to:
   ```
   http://localhost:3000
   ```

## Usage

1. **Upload RVM File**: 
   - <PERSON>ag and drop your RVM file onto the upload area, or
   - Click the upload area to browse and select a file

2. **Convert**: 
   - Click the "Convert to GLB" button
   - Wait for the conversion to complete

3. **Download**: 
   - Click the "Download GLB File" button to save the converted file

## Conversion Methods

The application supports multiple conversion backends:

### 1. Aspose.3D API (Recommended)
- High-quality conversion
- Preserves model hierarchy
- Requires API credentials

### 2. Local Tools (PmuTranslator)
- Uses RvmTranslator/PmuTranslator if installed
- Command-line based conversion
- No external dependencies

### 3. Fallback Demo
- Uses pre-converted demo file
- For testing and demonstration purposes

## Configuration

### Setting up Aspose.3D API

1. Sign up for an Aspose account
2. Get your API credentials
3. Update the server configuration:
   ```javascript
   // In server.js, update the convertWithAspose function
   const asposeConfig = {
       clientId: 'your-client-id',
       clientSecret: 'your-client-secret'
   };
   ```

### Installing PmuTranslator

1. Download RvmTranslator from GitHub
2. Extract to a folder in your PATH
3. Ensure `PmuTranslator.exe` is accessible from command line

## File Structure

```
rvm-to-glb-converter/
├── index.html          # Frontend interface
├── script.js           # Client-side JavaScript
├── server.js           # Backend server
├── package.json        # Node.js dependencies
├── README.md           # This file
├── uploads/            # Temporary upload directory (auto-created)
└── marine_rvm.glb      # Demo GLB file
```

## API Endpoints

### POST /convert
Uploads and converts an RVM file to GLB format.

**Request**: Multipart form data with `rvmFile` field
**Response**: GLB file download or error message

### GET /health
Health check endpoint.

**Response**: JSON with server status

## Development

### Running in Development Mode

```bash
npm run dev
```

This uses nodemon for automatic server restart on file changes.

### Adding New Conversion Methods

1. Create a new conversion function in `server.js`
2. Add it to the conversion chain in the `/convert` endpoint
3. Handle any specific error cases

## Deployment

### Local Deployment
The application runs on port 3000 by default. You can change this by setting the PORT environment variable:

```bash
PORT=8080 npm start
```

### Production Deployment
For production deployment:

1. Set NODE_ENV to production
2. Configure proper error logging
3. Set up file cleanup schedules
4. Configure HTTPS
5. Set up proper API rate limiting

## Troubleshooting

### Common Issues

1. **"No file uploaded" error**
   - Ensure the file has a .rvm extension
   - Check file size (max 100MB)

2. **"Conversion failed" error**
   - Verify conversion tools are installed
   - Check server logs for detailed error messages

3. **Server won't start**
   - Ensure Node.js is installed
   - Run `npm install` to install dependencies
   - Check if port 3000 is available

### Logs

Server logs are displayed in the console. For production, consider using a proper logging solution like Winston.

## Security Considerations

- File uploads are limited to 100MB
- Only .rvm files are accepted
- Uploaded files are automatically deleted after processing
- Consider adding rate limiting for production use

## License

MIT License - feel free to use and modify for your needs.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review server logs
3. Ensure all dependencies are properly installed

## Contributing

Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request
