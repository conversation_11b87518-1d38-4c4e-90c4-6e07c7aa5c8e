const { downloadRvmparserLinux } = require('./download-rvmparser-linux');
const fs = require('fs');
const path = require('path');

async function build() {
    console.log('🔧 Building RVM to GLB Converter Server...');
    console.log(`Platform: ${process.platform}`);
    
    // Check if we need to download rvmparser for Linux
    if (process.platform === 'linux') {
        console.log('📥 Linux platform detected - downloading rvmparser...');
        
        // Check if rvmparser already exists
        if (fs.existsSync('rvmparser')) {
            console.log('✅ rvmparser already exists');
        } else {
            const success = await downloadRvmparserLinux();
            if (!success) {
                console.error('❌ Failed to download rvmparser');
                process.exit(1);
            }
        }
    } else if (process.platform === 'win32') {
        console.log('🪟 Windows platform detected');
        if (fs.existsSync('rvmparser.exe')) {
            console.log('✅ rvmparser.exe already exists');
        } else {
            console.log('⚠️  rvmparser.exe not found - please ensure it exists for local development');
        }
    } else {
        console.log(`⚠️  Unsupported platform: ${process.platform}`);
    }
    
    console.log('✅ Server build completed successfully');
}

// Run if called directly
if (require.main === module) {
    build().catch(error => {
        console.error('❌ Build failed:', error.message);
        process.exit(1);
    });
}

module.exports = { build };
