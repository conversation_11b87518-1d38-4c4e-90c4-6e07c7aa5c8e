// Configuration for RVM to GLB Converter
// Automatically detects environment and sets appropriate API endpoints

window.APP_CONFIG = {
    // API Base URL - automatically detects environment
    API_BASE_URL: (() => {
        if (window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1' ||
            window.location.hostname.includes('192.168.') ||
            window.location.hostname.includes('10.0.') ||
            window.location.hostname.includes('172.')) {
            // Local development
            return 'http://localhost:3000';
        }

        // Production - update this with your actual Railway server URL
        return 'https://grse-marine-production.up.railway.app';
    })(),

    // Development flag
    IS_DEVELOPMENT: window.location.hostname === 'localhost' ||
        window.location.hostname === '127.0.0.1'
};

console.log('Environment:', window.APP_CONFIG.IS_DEVELOPMENT ? 'Development' : 'Production');
console.log('API Base URL:', window.APP_CONFIG.API_BASE_URL);
