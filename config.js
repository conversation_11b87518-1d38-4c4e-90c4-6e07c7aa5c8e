// Configuration for the RVM to GLB Converter
window.APP_CONFIG = {
    // API Base URL - automatically detects environment
    API_BASE_URL: (() => {
        // Check if we're running locally
        if (window.location.hostname === 'localhost' || 
            window.location.hostname === '127.0.0.1' || 
            window.location.hostname === '') {
            return 'http://localhost:3000';
        }
        
        // Production server URL
        return 'https://devserver-main--grse-marine.netlify.app';
    })(),
    
    // Other configuration options
    MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
    SUPPORTED_EXTENSIONS: ['.rvm'],
    
    // Development mode detection
    IS_DEVELOPMENT: window.location.hostname === 'localhost' || 
                   window.location.hostname === '127.0.0.1'
};

// Log the configuration in development
if (window.APP_CONFIG.IS_DEVELOPMENT) {
    console.log('App Configuration:', window.APP_CONFIG);
}
