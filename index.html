<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RVM to GLB Converter</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 20px;
            margin: 30px 0;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .upload-area.file-selected {
            border-color: #4caf50;
            background: #f8fff8;
        }

        .upload-icon {
            font-size: 4em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-text {
            font-size: 1.2em;
            color: #333;
            margin-bottom: 10px;
        }

        .upload-subtext {
            color: #666;
            font-size: 0.9em;
        }

        #fileInput {
            display: none;
        }

        .file-info {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            display: none;
        }

        .file-info.show {
            display: block;
        }

        .progress-container {
            margin: 20px 0;
            display: none;
        }

        .progress-container.show {
            display: block;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-fill.complete {
            background: linear-gradient(90deg, #4caf50, #45a049);
        }

        .progress-text {
            margin-top: 10px;
            color: #333;
            font-weight: 500;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .success-checkmark {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: #4caf50;
            border-radius: 50%;
            margin-right: 8px;
            position: relative;
        }

        .success-checkmark::after {
            content: '✓';
            color: white;
            font-size: 12px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .convert-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 10px;
            display: none;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .convert-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            background: #999;
        }

        .convert-btn:active {
            transform: translateY(0);
        }

        .convert-btn.show {
            display: inline-block;
        }

        .download-area {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            display: none;
        }

        .download-area.show {
            display: block;
        }

        .download-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .download-btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }

        .error-message {
            background: #ffe8e8;
            border: 1px solid #ff4444;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #cc0000;
            display: none;
        }

        .error-message.show {
            display: block;
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .features h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .features ul {
            list-style: none;
            padding-left: 0;
        }

        .features li {
            padding: 8px 0;
            color: #666;
        }

        .features li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>RVM to GLB Converter</h1>
            <p>Convert AVEVA Marine RVM files to GLB format for 3D viewing</p>
        </div>

        <div class="upload-area" id="uploadArea">
            <div class="upload-icon">📁</div>
            <div class="upload-text">Drop your RVM file here or click to browse</div>
            <div class="upload-subtext">Supports .rvm files from AVEVA Marine/PDMS</div>
        </div>

        <input type="file" id="fileInput" accept=".rvm" />

        <div class="file-info" id="fileInfo">
            <strong>Selected file:</strong> <span id="fileName"></span><br>
            <strong>Size:</strong> <span id="fileSize"></span>
        </div>

        <div class="error-message" id="errorMessage"></div>

        <button class="convert-btn" id="convertBtn">Convert to GLB</button>

        <div class="progress-container" id="progressContainer">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div class="progress-text" id="progressText">Converting...</div>
        </div>

        <div class="download-area" id="downloadArea">
            <h3>✅ Conversion Complete!</h3>
            <p>Your GLB file is ready for download</p>
            <a href="#" class="download-btn" id="downloadBtn" download>Download GLB File</a>
        </div>

        <!-- <div class="features">
            <h3>Features:</h3>
            <ul>
                <li>Convert AVEVA Marine RVM files to GLB format</li>
                <li>Preserves 3D geometry and hierarchy</li>
                <li>Compatible with Windows 3D Viewer and web browsers</li>
                <li>Fast and secure conversion</li>
                <li>No registration required</li>
            </ul>
        </div> -->
    </div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>

</html>