{"name": "rvm-to-glb-converter", "version": "1.0.0", "description": "Web application to convert AVEVA Marine RVM files to GLB format", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "node build.js || echo 'Build script failed, continuing...'", "postinstall": "npm run build", "install-deps": "npm install", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["rvm", "glb", "3d", "conversion", "aveva", "marine", "pdms"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}