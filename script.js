// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const convertBtn = document.getElementById('convertBtn');
const progressContainer = document.getElementById('progressContainer');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const downloadArea = document.getElementById('downloadArea');
const downloadBtn = document.getElementById('downloadBtn');
const errorMessage = document.getElementById('errorMessage');

let selectedFile = null;

// File size formatter
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Show error message
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.classList.add('show');
    setTimeout(() => {
        errorMessage.classList.remove('show');
    }, 5000);
}

// Hide all status elements
function hideAllStatus() {
    fileInfo.classList.remove('show');
    convertBtn.classList.remove('show');
    progressContainer.classList.remove('show');
    downloadArea.classList.remove('show');
    errorMessage.classList.remove('show');
}

// Handle file selection
function handleFileSelect(file) {
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.rvm')) {
        showError('Please select a valid RVM file (.rvm extension)');
        return;
    }

    // Validate file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
        showError('File size too large. Maximum size is 100MB.');
        return;
    }

    selectedFile = file;

    // Show file info
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);

    hideAllStatus();
    fileInfo.classList.add('show');
    convertBtn.classList.add('show');
}

// Upload area click handler
uploadArea.addEventListener('click', () => {
    fileInput.click();
});

// File input change handler
fileInput.addEventListener('change', (e) => {
    const file = e.target.files[0];
    handleFileSelect(file);
});

// Drag and drop handlers
uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// Convert button handler
convertBtn.addEventListener('click', async () => {
    if (!selectedFile) {
        showError('Please select a file first');
        return;
    }

    // Show progress
    convertBtn.style.display = 'none';
    progressContainer.classList.add('show');
    progressFill.style.width = '0%';
    progressText.textContent = 'Starting conversion...';

    try {
        await convertWithBackend();

    } catch (error) {
        console.error('Conversion error:', error);
        showError(error.message || 'Conversion failed. Please try again.');
        progressContainer.classList.remove('show');
        convertBtn.style.display = 'inline-block';
    }
});

// Convert using backend server
async function convertWithBackend() {
    try {
        // Update progress
        progressFill.style.width = '10%';
        progressText.textContent = 'Uploading file...';

        const formData = new FormData();
        formData.append('rvmFile', selectedFile);

        // Update progress
        progressFill.style.width = '30%';
        progressText.textContent = 'Processing conversion...';

        const response = await fetch(`${window.APP_CONFIG.API_BASE_URL}/convert`, {
            method: 'POST',
            body: formData
        });

        // Update progress
        progressFill.style.width = '80%';
        progressText.textContent = 'Finalizing...';

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(errorData.error || 'Conversion failed');
        }

        // Get the converted file as a blob
        const blob = await response.blob();
        const glbFileName = selectedFile.name.replace('.rvm', '.glb');

        // Update progress
        progressFill.style.width = '100%';
        progressText.textContent = 'Conversion complete!';

        // Create download URL
        const downloadUrl = URL.createObjectURL(blob);
        downloadBtn.href = downloadUrl;
        downloadBtn.download = glbFileName;

        // Show download area after a short delay
        setTimeout(() => {
            progressContainer.classList.remove('show');
            downloadArea.classList.add('show');
        }, 500);

        // Clean up the URL after download
        downloadBtn.addEventListener('click', () => {
            setTimeout(() => {
                URL.revokeObjectURL(downloadUrl);
            }, 1000);
        }, { once: true }); // Use once: true to prevent multiple listeners

    } catch (error) {
        throw new Error(error.message || 'Conversion service unavailable');
    }
}

// Reset form
function resetForm() {
    selectedFile = null;
    fileInput.value = '';
    hideAllStatus();
}

// Add reset functionality
document.addEventListener('DOMContentLoaded', () => {
    // Add a reset button if needed
    const resetBtn = document.createElement('button');
    resetBtn.textContent = 'Convert Another File';
    resetBtn.className = 'convert-btn';
    resetBtn.style.background = '#666';
    resetBtn.addEventListener('click', resetForm);

    downloadArea.appendChild(resetBtn);
});

// Handle download completion
downloadBtn.addEventListener('click', () => {
    // Track download analytics if needed
    console.log('GLB file downloaded:', downloadBtn.download);
});

// Prevent default drag behaviors on the document
document.addEventListener('dragover', (e) => e.preventDefault());
document.addEventListener('drop', (e) => e.preventDefault());
