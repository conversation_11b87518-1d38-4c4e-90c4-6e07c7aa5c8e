const https = require('https');
const fs = require('fs');
const path = require('path');

// Download the Linux version of rvmparser from GitHub releases
async function downloadRvmparserLinux() {
    const releaseUrl = 'https://api.github.com/repos/cdyk/rvmparser/releases/latest';
    
    console.log('Fetching latest release information...');
    
    try {
        // Get release information
        const releaseData = await fetchJson(releaseUrl);
        console.log(`Found release: ${releaseData.tag_name}`);
        
        // Find the Linux asset
        const linuxAsset = releaseData.assets.find(asset => 
            asset.name.includes('linux') && asset.name.includes('rvmparser')
        );
        
        if (!linuxAsset) {
            console.error('Linux binary not found in release assets');
            console.log('Available assets:');
            releaseData.assets.forEach(asset => console.log(`  - ${asset.name}`));
            return false;
        }
        
        console.log(`Downloading: ${linuxAsset.name}`);
        console.log(`Size: ${(linuxAsset.size / 1024 / 1024).toFixed(2)} MB`);
        
        // Download the file
        const success = await downloadFile(linuxAsset.browser_download_url, 'rvmparser');
        
        if (success) {
            // Make it executable (only on Unix-like systems)
            try {
                if (process.platform !== 'win32') {
                    fs.chmodSync('rvmparser', 0o755);
                    console.log('✅ Successfully downloaded and made rvmparser executable');
                } else {
                    console.log('✅ Successfully downloaded rvmparser (chmod skipped on Windows)');
                }
            } catch (error) {
                console.log('✅ Downloaded rvmparser (chmod failed, but file should work on Linux)');
            }
            console.log('The Linux rvmparser binary is now ready for deployment');
            return true;
        }
        
    } catch (error) {
        console.error('Error downloading rvmparser:', error.message);
        return false;
    }
}

// Helper function to fetch JSON from URL
function fetchJson(url) {
    return new Promise((resolve, reject) => {
        https.get(url, {
            headers: {
                'User-Agent': 'rvmparser-downloader'
            }
        }, (res) => {
            let data = '';
            
            res.on('data', chunk => data += chunk);
            res.on('end', () => {
                try {
                    resolve(JSON.parse(data));
                } catch (e) {
                    reject(new Error('Failed to parse JSON response'));
                }
            });
        }).on('error', reject);
    });
}

// Helper function to download file
function downloadFile(url, filename) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(filename);
        
        https.get(url, (response) => {
            // Handle redirects
            if (response.statusCode === 302 || response.statusCode === 301) {
                file.close();
                fs.unlink(filename, () => { });
                return downloadFile(response.headers.location, filename).then(resolve).catch(reject);
            }
            
            if (response.statusCode !== 200) {
                reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
                return;
            }
            
            const totalSize = parseInt(response.headers['content-length'], 10);
            let downloadedSize = 0;
            
            response.on('data', (chunk) => {
                downloadedSize += chunk.length;
                const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
                process.stdout.write(`\rProgress: ${progress}%`);
            });
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log('\n✅ Download completed');
                resolve(true);
            });
            
        }).on('error', (err) => {
            fs.unlink(filename, () => {}); // Delete partial file
            reject(err);
        });
    });
}

// Run if called directly
if (require.main === module) {
    downloadRvmparserLinux().then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { downloadRvmparserLinux };
