const express = require('express');
const multer = require('multer');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

const app = express();
const PORT = process.env.PORT || 3000;

// Enable CORS for all routes
app.use(cors());

// Serve static files from client directory
app.use(express.static(path.join(__dirname, '../client')));

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(__dirname, 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, 'rvmFile-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({ 
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    },
    fileFilter: (req, file, cb) => {
        if (path.extname(file.originalname).toLowerCase() === '.rvm') {
            cb(null, true);
        } else {
            cb(new Error('Only .rvm files are allowed'));
        }
    }
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../client/index.html'));
});

// File conversion endpoint
app.post('/convert', upload.single('rvmFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const inputPath = req.file.path;
        const originalName = req.file.originalname;
        const outputName = originalName.replace(/\.rvm$/i, '.glb');
        const outputPath = path.join(path.dirname(inputPath), outputName);

        console.log(`Converting ${originalName} to GLB...`);
        console.log(`Input path: ${inputPath}`);
        console.log(`Output path: ${outputPath}`);

        // Convert using rvmparser
        await convertWithRvmParser(inputPath, outputPath);

        // Check if conversion was successful
        if (!fs.existsSync(outputPath)) {
            throw new Error('Conversion failed - output file not created');
        }

        const stats = fs.statSync(outputPath);
        console.log(`Conversion successful! Output file size: ${stats.size} bytes`);

        // Send the converted file
        res.download(outputPath, outputName, (err) => {
            if (err) {
                console.error('Error sending file:', err);
                if (!res.headersSent) {
                    res.status(500).json({ error: 'Error sending converted file' });
                }
            }

            // Clean up files after sending
            setTimeout(() => {
                try {
                    if (fs.existsSync(inputPath)) fs.unlinkSync(inputPath);
                    if (fs.existsSync(outputPath)) fs.unlinkSync(outputPath);
                    console.log('Temporary files cleaned up');
                } catch (cleanupError) {
                    console.error('Error cleaning up files:', cleanupError);
                }
            }, 5000); // Wait 5 seconds before cleanup
        });

    } catch (error) {
        console.error('Conversion error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Conversion function using rvmparser
async function convertWithRvmParser(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        // Detect platform and use appropriate executable
        const isWindows = process.platform === 'win32';
        const rvmparserName = isWindows ? 'rvmparser.exe' : 'rvmparser';
        const rvmparserPath = path.join(__dirname, rvmparserName);

        // Check if the executable exists
        if (!fs.existsSync(rvmparserPath)) {
            const platform = process.platform;
            reject(new Error(`rvmparser executable not found for platform: ${platform}. Expected: ${rvmparserPath}`));
            return;
        }

        // Command: rvmparser --output-gltf=output.glb input.rvm
        const args = [`--output-gltf=${outputPath}`, inputPath];

        console.log(`Platform: ${process.platform}`);
        console.log(`Starting rvmparser conversion: ${rvmparserPath} ${args.join(' ')}`);

        const rvmProcess = spawn(rvmparserPath, args, {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        rvmProcess.stdout.on('data', (data) => {
            stdout += data.toString();
            console.log('rvmparser stdout:', data.toString());
        });

        rvmProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            console.log('rvmparser stderr:', data.toString());
        });

        rvmProcess.on('close', (code) => {
            if (code === 0) {
                console.log('rvmparser conversion completed successfully');
                resolve({ success: true, stdout, stderr });
            } else {
                console.error('rvmparser conversion failed with code:', code);
                reject(new Error(`rvmparser process exited with code ${code}. Stderr: ${stderr}`));
            }
        });

        rvmProcess.on('error', (error) => {
            console.error('Failed to start rvmparser process:', error);
            reject(error);
        });
    });
}

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 100MB.' });
        }
    }
    console.error('Unhandled error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log(`RVM to GLB Converter Server running on port ${PORT}`);
    console.log(`Platform: ${process.platform}`);
    console.log(`Serving frontend from: ${path.join(__dirname, '../client')}`);
});

module.exports = app;
