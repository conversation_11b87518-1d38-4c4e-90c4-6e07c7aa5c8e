const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.static('.'));
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = './uploads';
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, file.fieldname + '-' + uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
    },
    fileFilter: (req, file, cb) => {
        if (path.extname(file.originalname).toLowerCase() === '.rvm') {
            cb(null, true);
        } else {
            cb(new Error('Only RVM files are allowed'));
        }
    }
});

// Serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Upload and convert endpoint
app.post('/convert', upload.single('rvmFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({ error: 'No file uploaded' });
        }

        const inputPath = req.file.path;
        const outputPath = inputPath.replace('.rvm', '.glb');
        const originalName = req.file.originalname;
        const outputName = originalName.replace('.rvm', '.glb');

        console.log(`Converting ${originalName} to GLB...`);
        console.log(`Input path: ${inputPath}`);
        console.log(`Output path: ${outputPath}`);

        // Convert using rvmparser
        await convertWithRvmParser(inputPath, outputPath);

        // Check if conversion was successful
        if (!fs.existsSync(outputPath)) {
            throw new Error('Conversion failed - output file not created');
        }

        console.log(`Sending file: ${outputPath} as ${outputName}`);
        console.log(`File size: ${fs.statSync(outputPath).size} bytes`);

        // Set proper headers for file download
        res.setHeader('Content-Type', 'application/octet-stream');
        res.setHeader('Content-Disposition', `attachment; filename="${outputName}"`);
        res.setHeader('Content-Length', fs.statSync(outputPath).size);

        // Send the file
        const fileStream = fs.createReadStream(outputPath);
        fileStream.pipe(res);

        fileStream.on('end', () => {
            console.log('File sent successfully');

            // Clean up files after download
            setTimeout(() => {
                try {
                    if (fs.existsSync(inputPath)) {
                        fs.unlinkSync(inputPath);
                        console.log('Cleaned up input file');
                    }
                    if (fs.existsSync(outputPath)) {
                        fs.unlinkSync(outputPath);
                        console.log('Cleaned up output file');
                    }
                } catch (cleanupError) {
                    console.error('Cleanup error:', cleanupError);
                }
            }, 2000);
        });

        fileStream.on('error', (err) => {
            console.error('File stream error:', err);
            if (!res.headersSent) {
                res.status(500).json({ error: 'File transfer failed' });
            }
        });

    } catch (error) {
        console.error('Conversion error:', error);
        res.status(500).json({ error: error.message });
    }
});

// Conversion function using rvmparser
async function convertWithRvmParser(inputPath, outputPath) {
    return new Promise((resolve, reject) => {
        const rvmparserPath = path.join(__dirname, 'rvmparser.exe');

        // Command: rvmparser.exe --output-gltf=output.glb input.rvm
        const args = [`--output-gltf=${outputPath}`, inputPath];

        console.log(`Starting rvmparser conversion: ${rvmparserPath} ${args.join(' ')}`);

        const rvmProcess = spawn(rvmparserPath, args, {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        let stdout = '';
        let stderr = '';

        rvmProcess.stdout.on('data', (data) => {
            stdout += data.toString();
            console.log('rvmparser stdout:', data.toString());
        });

        rvmProcess.stderr.on('data', (data) => {
            stderr += data.toString();
            console.log('rvmparser stderr:', data.toString());
        });

        rvmProcess.on('close', (code) => {
            if (code === 0) {
                console.log('rvmparser conversion completed successfully');
                resolve({ success: true, stdout, stderr });
            } else {
                console.error('rvmparser conversion failed with code:', code);
                reject(new Error(`rvmparser process exited with code ${code}. Stderr: ${stderr}`));
            }
        });

        rvmProcess.on('error', (error) => {
            console.error('Failed to start rvmparser process:', error);
            reject(error);
        });
    });
}

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({ error: 'File too large. Maximum size is 100MB.' });
        }
    }

    console.error('Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// Start server
app.listen(PORT, () => {
    console.log(`RVM to GLB Converter Server running on port ${PORT}`);
    console.log(`Open http://localhost:${PORT} in your browser`);
});

module.exports = app;
